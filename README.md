# Video Upload Automation Hub

A comprehensive web application for automating video uploads to 5 different video hosting platforms simultaneously. Built with modern technologies and a sleek dark mode interface.

## 🚀 Features

### Core Functionality
- **Multi-Platform Upload**: Upload to 5 platforms simultaneously (Lulustream, StreamP2P, RPMShare, FileMoon, UpnShare)
- **Queue Management**: Handle up to 50 URLs with intelligent queuing (5 concurrent uploads)
- **Real-time Progress**: Live progress tracking with speed, percentage, and ETA for each platform
- **Embed Code Extraction**: Automatically extract platform-specific embed codes
- **CSV Export**: Generate CSV files with filenames and embed codes in specified format
- **Audio Notifications**: Custom completion and error sound notifications

### Technical Features
- **WebSocket Communication**: Real-time updates without page refresh
- **Deep Black Dark Mode**: Professional dark theme with proper text visibility
- **Responsive Design**: Works on desktop and mobile devices
- **Error Handling**: Comprehensive error handling with fallback strategies
- **Storage**: JSON file-based persistence (perfect for local deployment)
- **API Key Management**: Secure environment variable configuration

## 🏗️ Architecture

### Backend (FastAPI)
- **Framework**: FastAPI with async/await support
- **Storage**: JSON file-based storage (no external database required)
- **WebSocket**: Real-time communication for progress updates
- **HTTP Client**: aiohttp for concurrent API requests
- **Authentication**: API key-based authentication for video platforms

### Frontend (React)
- **Framework**: React 18 with functional components and hooks
- **Styling**: Tailwind CSS with custom dark theme
- **Icons**: Lucide React for consistent iconography
- **Notifications**: React Hot Toast for user feedback
- **State Management**: React hooks with WebSocket integration

### Video Hosting Platform Integrations

#### 1. Lulustream
- **API Type**: GET-based with key parameter
- **Base URL**: `https://lulustream.com/api`
- **Embed Format**: `<iframe src="https://luluvid.com/e/{file_code}" ...>`

#### 2. StreamP2P
- **API Type**: POST-based with api-token header
- **Base URL**: `https://streamp2p.com/api/v1`
- **Embed Format**: `<iframe src="https://streamdb.p2pstream.online/#{file_code}" ...>`

#### 3. RPMShare
- **API Type**: POST-based with api-token header
- **Base URL**: `https://rpmshare.com/api/v1`
- **Embed Format**: `<iframe src="https://streamdb.rpmstream.online/#{file_code}" ...>`

#### 4. FileMoon
- **API Type**: GET-based with key parameter
- **Base URL**: `https://filemoonapi.com/api`
- **Embed Format**: `<iframe src="https://filemoon.to/e/{file_code}" ...>`

#### 5. UpnShare
- **API Type**: POST-based with api-token header
- **Base URL**: `https://upnshare.com/api/v1`
- **Embed Format**: `<iframe src="https://streamdb.upns.online/#{file_code}" ...>`

## 📁 Project Structure

```
/app/
├── backend/                    # FastAPI backend
│   ├── server.py              # Main application file
│   ├── requirements.txt       # Python dependencies
│   ├── .env                   # Environment variables
│   └── audio/                 # Custom audio files directory
├── frontend/                  # React frontend
│   ├── src/
│   │   ├── App.js            # Main React component
│   │   ├── App.css           # Component styles
│   │   ├── index.js          # Entry point
│   │   └── index.css         # Global styles with Tailwind
│   ├── public/
│   │   ├── index.html        # HTML template
│   │   ├── manifest.json     # PWA manifest
│   │   └── audio/            # Frontend audio files
│   ├── package.json          # Node dependencies
│   └── .env                  # Frontend environment
├── API Endpoints.txt         # Platform API documentation
├── Video Hosting Platforms API Configuration Documentation.txt
├── .gitignore               # Git ignore rules
└── README.md               # This file
```

## 🔧 Installation & Setup

### Prerequisites
- Python 3.11+
- Node.js 18+
- **No external dependencies required** (MongoDB replaced with file-based storage)
- API keys for all 5 video hosting platforms

### Environment Configuration

#### Backend (.env)
```bash
# Video Hosting Platform API Keys
LULUSTREAM_API_KEY=your_lulustream_api_key_here
STREAMP2P_API_KEY=your_streamp2p_api_key_here
RPMSHARE_API_KEY=your_rpmshare_api_key_here
FILEMOON_API_KEY=your_filemoon_api_key_here
UPNSHARE_API_KEY=your_upnshare_api_key_here

# Server Configuration
BACKEND_HOST=0.0.0.0
BACKEND_PORT=8001

# File Upload Configuration
MAX_CONCURRENT_UPLOADS=5
MAX_QUEUE_SIZE=50
UPLOAD_TIMEOUT_MINUTES=30
```

#### Frontend (.env)
```bash
REACT_APP_BACKEND_URL=http://localhost:8001
REACT_APP_AUDIO_ENABLED=true
REACT_APP_COMPLETION_AUDIO=/audio/completion.mp3
REACT_APP_ERROR_AUDIO=/audio/error.mp3
```

### Installation Steps

1. **Install Backend Dependencies**
   ```bash
   cd backend
   pip install -r requirements.txt
   ```

2. **Install Frontend Dependencies**
   ```bash
   cd frontend
   yarn install
   ```

3. **Configure API Keys**
   - Update `.env` files with your actual API keys
   - Ensure MongoDB is running on the configured URL

4. **Add Custom Audio Files (Optional):**
   - Place `completion.mp3` and `error.mp3` in:
     - `/backend/audio/`
     - `/frontend/public/audio/`
   - These files are now tracked in git for easy deployment

5. **Start Services**
   ```bash
   # Start backend
   cd backend
   python server.py

   # Start frontend (in new terminal)
   cd frontend
   yarn start
   ```

## 📊 Usage Workflow

### 1. URL Input
- Enter remote video URLs (one per line)
- Maximum 50 URLs per batch
- Support for various video file formats

### 2. Queue Management
- URLs are automatically queued for upload
- Maximum 5 concurrent uploads to prevent API rate limiting
- Real-time queue status with pending/active/completed counts

### 3. Upload Process
- Each URL is uploaded to all 5 platforms simultaneously
- Real-time progress tracking for each platform:
  - Upload percentage (0-100%)
  - Upload speed (MB/s)
  - Estimated time remaining
  - Platform-specific status

### 4. Progress Monitoring
- **Queued**: Waiting in queue
- **Uploading**: Initial upload submission (0-25%)
- **Processing**: Platform processing (25-95%)
- **Completed**: Upload finished with embed code (100%)
- **Failed**: Upload encountered error

### 5. Embed Code Collection
- Automatically extracts embed codes in specified format
- Platform-specific iframe templates
- Real-time embed code availability indicators

### 6. CSV Export
- Downloads CSV file with format:
  ```
  Filename,Embed Codes
  video1.mp4,"<lulustream_embed>

  <streamp2p_embed>

  <rpmshare_embed>

  <filemoon_embed>

  <upnshare_embed>"
  ```

## 🔊 Audio Notifications

### Completion Sound
- Plays when all platforms complete upload for a file
- Custom `.mp3` file: `completion.mp3`
- Configurable volume and enable/disable

### Error Sound
- Plays when upload fails on any platform
- Custom `.mp3` file: `error.mp3`
- Immediate feedback for issues

## 🎨 User Interface

### Design Philosophy
- **Deep Black Theme**: Professional dark mode (#000000 background)
- **Colorful Platform Icons**: Distinct colors for each platform
- **High Contrast**: Excellent text visibility on dark background
- **Responsive Layout**: Works on all screen sizes
- **Real-time Updates**: Live progress without page refresh

### Platform Color Scheme
- **Lulustream**: Purple (🟣)
- **StreamP2P**: Blue (🔵)
- **RPMShare**: Green (🟢)
- **FileMoon**: Yellow (🟡)
- **UpnShare**: Pink (🩷)

### Key UI Components
- **Header**: App title with connection status
- **Platform Status Cards**: API key configuration status
- **URL Input Section**: Textarea with queue/upload buttons
- **Queue Overview**: Statistics cards with counts
- **Active Uploads**: Real-time progress for each platform
- **Upload Queue**: Pending uploads list
- **Completed Uploads**: Recent successful uploads

## 🛡️ Error Handling

### Platform-Specific Strategies
- **Timeout Fallback**: Assumes completion after extended polling
- **Retry Logic**: Exponential backoff for transient errors
- **Error Classification**: Distinguishes between temporary and permanent failures
- **Graceful Degradation**: Continues with successful platforms

### Common Issues
- **API Rate Limiting**: Automatic throttling and retry
- **Network Timeouts**: Extended timeout periods with fallback
- **Invalid URLs**: Validation and user feedback
- **Missing API Keys**: Clear configuration status indicators

## 🔒 Security Features

### API Key Management
- Environment variable storage
- No API keys in source code
- Secure .env file exclusion in .gitignore

### Input Validation
- URL format validation
- File size and type restrictions
- Queue size limitations
- Session management

## 📈 Performance Optimizations

### Concurrent Processing
- Asynchronous upload handling
- Platform-parallel processing
- Connection pooling
- Memory-efficient queue management

### Real-time Updates
- WebSocket for live progress
- Minimal data transfer
- Efficient state management
- Automatic reconnection

## 🧪 Testing Recommendations

### Manual Testing
1. **Single URL Upload**: Test with one video URL
2. **Batch Upload**: Test with multiple URLs (5-10)
3. **Queue Management**: Test queue with 50+ URLs
4. **Error Scenarios**: Test with invalid URLs
5. **Network Issues**: Test with intermittent connectivity
6. **Audio Notifications**: Verify completion/error sounds

### API Testing
1. **Platform Integration**: Test each platform independently
2. **Authentication**: Verify API key functionality
3. **Progress Tracking**: Ensure accurate progress reporting
4. **Embed Code Extraction**: Verify correct embed format
5. **CSV Generation**: Test export functionality

## 🚀 Deployment

### Production Considerations
- **Environment Variables**: Set production API keys
- **Database**: Configure MongoDB connection
- **CORS**: Update allowed origins
- **SSL/HTTPS**: Enable secure connections
- **Monitoring**: Add logging and monitoring
- **Backup**: Regular database backups

### Docker Support (Future Enhancement)
```dockerfile
# Dockerfile example for containerization
FROM python:3.11-slim
WORKDIR /app
COPY requirements.txt .
RUN pip install -r requirements.txt
COPY . .
EXPOSE 8001
CMD ["python", "server.py"]
```

## 🤝 Contributing

### Development Setup
1. Fork the repository
2. Create feature branch
3. Install dependencies
4. Make changes
5. Test thoroughly
6. Submit pull request

### Code Standards
- Python: PEP 8 compliance
- JavaScript: ESLint configuration
- CSS: Tailwind CSS utilities
- Documentation: Clear comments and docstrings

## 📝 API Documentation

### Backend Endpoints

#### Upload Management
- `POST /api/upload/submit` - Submit URLs for upload
- `GET /api/queue/status` - Get current queue status
- `DELETE /api/upload/cancel/{task_id}` - Cancel specific upload

#### Data Export
- `GET /api/download/csv` - Download embed codes CSV
- `GET /api/download/csv?session_id={id}` - Download session-specific CSV

#### System Status
- `GET /api/health` - Health check endpoint
- `GET /api/platforms/status` - Platform configuration status
- `DELETE /api/uploads/clear-completed` - Clear completed uploads

#### WebSocket
- `WS /ws` - Real-time updates connection

## 🔮 Future Enhancements

### Planned Features
- **Torrent Support**: Direct magnet link processing
- **Batch Operations**: Bulk edit/delete functionality
- **Analytics Dashboard**: Upload statistics and insights
- **User Authentication**: Multi-user support with sessions
- **API Rate Limiting**: Smart throttling per platform
- **Webhook Support**: External integration capabilities

### Technical Improvements
- **Database Optimization**: Indexing and query optimization
- **Caching Layer**: Redis integration for performance
- **Load Balancing**: Multiple backend instances
- **Monitoring**: Comprehensive logging and metrics
- **Testing Suite**: Automated testing framework

## 📞 Support

### Troubleshooting
1. **Services Not Starting**: Check supervisor logs
2. **Database Connection**: Verify MongoDB status
3. **API Errors**: Validate API keys in platform settings
4. **Frontend Issues**: Check browser console for errors
5. **Audio Not Playing**: Verify audio file placement

### Log Files
- Backend: `tail -f /var/log/supervisor/backend.*.log`
- Frontend: Browser developer console
- MongoDB: `/var/log/mongodb/mongod.log`
- Supervisor: `/var/log/supervisor/supervisord.log`




Excellent! Now here are the **complete detailed steps to run this app on your local machine:**

# 🚀 **Complete Setup Guide - Video Upload Automation Hub**

## 📋 **Prerequisites**
- **Python 3.11+** (Download from https://python.org)
- **Node.js 18+** (Download from https://nodejs.org)
- **Git** (for downloading the code)

## 📁 **Step 1: Get the Application**

### Option A: If you have the code already
```bash
# Navigate to your project directory
cd /path/to/video-upload-automation-hub
```

### Option B: If downloading fresh
```bash
# Clone or download the repository
git clone <your-repo-url>
cd video-upload-automation-hub
```

## 🔧 **Step 2: Setup Backend**

### 2.1 Install Python Dependencies
```bash
# Navigate to backend directory
cd backend

# Install required packages
pip install -r requirements.txt
```

### 2.2 Configure API Keys
```bash
# Edit the .env file with your API keys
nano .env
# OR use any text editor like notepad, vscode, etc.
```

**Edit the .env file to look like this:**
```bash
# Video Hosting Platform API Keys
LULUSTREAM_API_KEY=your_actual_lulustream_key_here
STREAMP2P_API_KEY=your_actual_streamp2p_key_here
RPMSHARE_API_KEY=your_actual_rpmshare_key_here
FILEMOON_API_KEY=your_actual_filemoon_key_here
UPNSHARE_API_KEY=your_actual_upnshare_key_here

# Server Configuration
BACKEND_HOST=0.0.0.0
BACKEND_PORT=8001

# File Upload Configuration
MAX_CONCURRENT_UPLOADS=5
MAX_QUEUE_SIZE=50
UPLOAD_TIMEOUT_MINUTES=30
```

### 2.3 Start the Backend Server
```bash
# From the backend directory
python server.py
```

**You should see:**
```
INFO:     Started server process
INFO:     Waiting for application startup.
File-based storage initialized successfully
INFO:     Application startup complete.
INFO:     Uvicorn running on http://0.0.0.0:8001
```

**✅ Backend is now running on http://localhost:8001**

## 🎨 **Step 3: Setup Frontend** (Open a NEW terminal/command prompt)

### 3.1 Install Node.js Dependencies
```bash
# Navigate to frontend directory (from project root)
cd frontend

# Install packages using yarn (preferred) or npm
yarn install
# OR if you don't have yarn: npm install
```

### 3.2 Start the Frontend
```bash
# From the frontend directory
yarn start
# OR: npm start
```

**You should see:**
```
Compiled successfully!

You can now view video-upload-automation-frontend in the browser.

  Local:            http://localhost:3000
  On Your Network:  http://192.168.x.x:3000
```

**✅ Frontend is now running on http://localhost:3000**

## 🎯 **Step 4: Access the Application**

### Open your web browser and go to:
```
http://localhost:3000
```

**You should see:**
- ✅ Deep black interface with colorful platform icons
- ✅ All 5 platforms showing "Ready" status (with your API keys)
- ✅ URL input textarea
- ✅ Control buttons: Queue for Upload, Download CSV, Clear Completed, Clear Queue, End All Tasks

## 🧪 **Step 5: Test the Application**

### 5.1 Basic Test
1. **Enter a test URL in the textarea:**
```
https://sample-videos.com/zip/10/mp4/SampleVideo_1280x720_1mb.mp4
```

2. **Click "Queue for Upload"**

3. **Watch the magic happen:**
   - See real-time progress for all 5 platforms
   - Monitor upload speeds and ETA
   - Watch embed codes appear as uploads complete

### 5.2 Batch Test
Enter multiple URLs (one per line):
```
https://sample-videos.com/zip/10/mp4/SampleVideo_1280x720_1mb.mp4
https://sample-videos.com/zip/10/mp4/SampleVideo_1280x720_2mb.mp4
https://file-examples.com/storage/fe68b16cd6e0a6085542cd4/2017/10/file_example_MP4_480_1_5MG.mp4
```

### 5.3 Control Features
- **End Task**: Click ❌ button next to any in-progress upload
- **Clear Completed**: Remove completed uploads from display
- **Clear Queue**: Clear all queued and active uploads
- **End All Tasks**: Stop everything with one click
- **Download CSV**: Get embed codes file when uploads complete

## 📂 **File Structure on Your PC**
```
your-project-folder/
├── backend/
│   ├── server.py          # Main backend application
│   ├── .env               # Your API keys (KEEP PRIVATE)
│   ├── requirements.txt   # Python dependencies
│   ├── data/             # Data storage folder
│   │   └── tasks.json    # Upload history (auto-created)
│   └── audio/            # Place your custom .mp3 files here
├── frontend/
│   ├── src/
│   │   ├── App.js        # Main React component
│   │   └── ...
│   ├── public/
│   │   └── audio/        # Place your .mp3 files here too
│   └── package.json      # Node.js dependencies
└── README.md             # Documentation
```

## 🔊 **Optional: Add Custom Audio**
1. **Place your custom audio files:**
   - `completion.mp3` → when all uploads complete
   - `error.mp3` → when uploads fail

2. **Copy to both locations:**
   - `/backend/audio/completion.mp3` and `/backend/audio/error.mp3`
   - `/frontend/public/audio/completion.mp3` and `/frontend/public/audio/error.mp3`

## 🚨 **Troubleshooting**

### Backend Issues:
```bash
# Check if backend is running
curl http://localhost:8001/api/health

# View backend logs (if any errors)
# Check the terminal where you ran 'python server.py'
```

### Frontend Issues:
```bash
# Check if frontend is running
# Open browser to http://localhost:3000

# Check browser console (F12) for any errors
```

### Port Conflicts:
If ports 3000 or 8001 are already in use:
- **Backend**: Edit `.env` file and change `BACKEND_PORT=8001` to another port
- **Frontend**: It will automatically try port 3001, 3002, etc.

## 🎉 **You're All Set!**

**✅ Backend Running:** http://localhost:8001
**✅ Frontend Running:** http://localhost:3000  
**✅ Full Functionality:** Upload, Progress, CSV Download, Task Control
**✅ Real-time Updates:** WebSocket connected
**✅ All 5 Platforms:** Ready with your API keys

**Start uploading videos to all 5 platforms simultaneously and enjoy the automation! 🚀**

---

**The app is now fully running and ready for your testing with all the new control buttons you requested!** 

Test it now with some video URLs and let me know how it works! 🎯



## 📄 License

This project is developed for video upload automation purposes. Please ensure compliance with all video hosting platform terms of service when using their APIs.

---

**Video Upload Automation Hub** - Built with ❤️ for efficient multi-platform video management.