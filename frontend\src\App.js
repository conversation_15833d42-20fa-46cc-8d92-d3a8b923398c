import React, { useState, useEffect, useRef, useCallback } from 'react';
import { 
  Upload, 
  Play, 
  Pause, 
  Download, 
  Trash2, 
  CheckCircle, 
  XCircle, 
  Clock, 
  AlertTriangle,
  Loader2,
  Plus,
  Monitor,
  Zap,
  Globe,
  Database,
  BarChart3
} from 'lucide-react';
import toast, { Toaster } from 'react-hot-toast';
import './App.css';

// Get backend URL from environment
const BACKEND_URL = process.env.REACT_APP_BACKEND_URL || 'http://localhost:8001';

// Platform configurations
const PLATFORMS = [
  { name: 'lulustream', display: 'Lulustream', color: 'purple', icon: '🟣' },
  { name: 'streamp2p', display: 'StreamP2P', color: 'blue', icon: '🔵' },
  { name: 'rpmshare', display: 'RPMShare', color: 'green', icon: '🟢' },
  { name: 'filemoon', display: 'FileMoon', color: 'yellow', icon: '🟡' },
  { name: 'upnshare', display: 'UpnShare', color: 'pink', icon: '🩷' }
];

function App() {
  // State management
  const [urls, setUrls] = useState('');
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [queueStatus, setQueueStatus] = useState({
    total_queued: 0,
    total_in_progress: 0,
    total_completed: 0,
    total_failed: 0,
    current_queue: [],
    active_uploads: [],
    completed_uploads: []
  });
  const [sessionId, setSessionId] = useState(null);
  const [platformStatus, setPlatformStatus] = useState({});
  
  // WebSocket connection
  const wsRef = useRef(null);
  const reconnectTimeoutRef = useRef(null);
  const audioRef = useRef(null);
  
  // Audio notification system
  const playCompletionSound = useCallback(() => {
    try {
      const audio = new Audio('/audio/completion.mp3');
      audio.volume = 0.3;
      audio.play().catch(e => console.log('Could not play completion sound:', e));
    } catch (e) {
      console.log('Completion audio not available');
    }
  }, []);
  
  const playErrorSound = useCallback(() => {
    try {
      const audio = new Audio('/audio/error.mp3');
      audio.volume = 0.3;
      audio.play().catch(e => console.log('Could not play error sound:', e));
    } catch (e) {
      console.log('Error audio not available');
    }
  }, []);
  
  // WebSocket connection management
  const connectWebSocket = useCallback(() => {
    if (wsRef.current?.readyState === WebSocket.OPEN) {
      return;
    }
    
    const wsUrl = BACKEND_URL.replace('http', 'ws') + '/ws';
    const ws = new WebSocket(wsUrl);
    
    ws.onopen = () => {
      console.log('WebSocket connected');
      wsRef.current = ws;
      if (reconnectTimeoutRef.current) {
        clearTimeout(reconnectTimeoutRef.current);
        reconnectTimeoutRef.current = null;
      }
    };
    
    ws.onmessage = (event) => {
      try {
        const data = JSON.parse(event.data);
        
        if (data.type === 'initial_state') {
          setQueueStatus({
            total_queued: data.data.queue.length,
            total_in_progress: data.data.active.length,
            total_completed: data.data.completed.length,
            total_failed: data.data.failed.length,
            current_queue: data.data.queue,
            active_uploads: data.data.active,
            completed_uploads: data.data.completed
          });
        } else if (data.type === 'task_update') {
          // Handle real-time task updates
          const task = data.data;
          
          // Check if task just completed all platforms
          const completedPlatforms = Object.values(task.platform_status || {}).filter(status => status === 'completed').length;
          const totalPlatforms = PLATFORMS.length;
          
          if (completedPlatforms === totalPlatforms && task.status === 'completed') {
            playCompletionSound();
            toast.success(`✅ Upload completed: ${task.filename}`, {
              duration: 4000,
              style: {
                background: '#065f46',
                color: '#ffffff',
                border: '1px solid #10b981'
              }
            });
          } else if (task.status === 'failed') {
            playErrorSound();
            toast.error(`❌ Upload failed: ${task.filename}`, {
              duration: 4000,
              style: {
                background: '#7f1d1d',
                color: '#ffffff',
                border: '1px solid #ef4444'
              }
            });
          }
          
          // Refresh queue status
          fetchQueueStatus();
        }
      } catch (e) {
        console.error('Error parsing WebSocket message:', e);
      }
    };
    
    ws.onclose = () => {
      console.log('WebSocket disconnected');
      wsRef.current = null;
      
      // Reconnect after 3 seconds
      reconnectTimeoutRef.current = setTimeout(() => {
        connectWebSocket();
      }, 3000);
    };
    
    ws.onerror = (error) => {
      console.error('WebSocket error:', error);
    };
    
  }, [playCompletionSound, playErrorSound]);
  
  // Fetch queue status
  const fetchQueueStatus = useCallback(async () => {
    try {
      const response = await fetch(`${BACKEND_URL}/api/queue/status`);
      if (response.ok) {
        const data = await response.json();
        setQueueStatus(data);
      }
    } catch (error) {
      console.error('Error fetching queue status:', error);
    }
  }, []);
  
  // Fetch platform status
  const fetchPlatformStatus = useCallback(async () => {
    try {
      const response = await fetch(`${BACKEND_URL}/api/platforms/status`);
      if (response.ok) {
        const data = await response.json();
        setPlatformStatus(data);
      }
    } catch (error) {
      console.error('Error fetching platform status:', error);
    }
  }, []);
  
  // Initialize on component mount
  useEffect(() => {
    connectWebSocket();
    fetchQueueStatus();
    fetchPlatformStatus();
    
    // Set up periodic refresh
    const interval = setInterval(() => {
      fetchQueueStatus();
    }, 10000); // Refresh every 10 seconds
    
    return () => {
      clearInterval(interval);
      if (wsRef.current) {
        wsRef.current.close();
      }
      if (reconnectTimeoutRef.current) {
        clearTimeout(reconnectTimeoutRef.current);
      }
    };
  }, [connectWebSocket, fetchQueueStatus, fetchPlatformStatus]);
  
  // Handle URL submission
  const handleSubmit = async (e) => {
    e.preventDefault();
    
    if (!urls.trim()) {
      toast.error('Please enter at least one URL');
      return;
    }
    
    const urlList = urls.split('\n').filter(url => url.trim()).map(url => url.trim());
    
    if (urlList.length === 0) {
      toast.error('Please enter valid URLs');
      return;
    }
    
    if (urlList.length > 50) {
      toast.error('Maximum 50 URLs allowed at once');
      return;
    }
    
    setIsSubmitting(true);
    
    try {
      const response = await fetch(`${BACKEND_URL}/api/upload/submit`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          urls: urlList,
          session_id: sessionId
        }),
      });
      
      if (response.ok) {
        const data = await response.json();
        setSessionId(data.session_id);
        setUrls(''); // Clear input
        
        toast.success(`✅ Queued ${urlList.length} URLs for upload`, {
          duration: 3000,
          style: {
            background: '#065f46',
            color: '#ffffff',
            border: '1px solid #10b981'
          }
        });
        
        // Refresh status
        fetchQueueStatus();
      } else {
        const error = await response.json();
        toast.error(`❌ Error: ${error.detail}`);
      }
    } catch (error) {
      toast.error(`❌ Network error: ${error.message}`);
    } finally {
      setIsSubmitting(false);
    }
  };
  
  // Handle CSV download
  const handleDownloadCSV = async () => {
    try {
      const url = sessionId 
        ? `${BACKEND_URL}/api/download/csv?session_id=${sessionId}`
        : `${BACKEND_URL}/api/download/csv`;
        
      const response = await fetch(url);
      
      if (response.ok) {
        const blob = await response.blob();
        const downloadUrl = window.URL.createObjectURL(blob);
        const a = document.createElement('a');
        a.href = downloadUrl;
        a.download = `embed_codes_${new Date().toISOString().slice(0, 19).replace(/:/g, '-')}.csv`;
        document.body.appendChild(a);
        a.click();
        document.body.removeChild(a);
        window.URL.revokeObjectURL(downloadUrl);
        
        toast.success('✅ CSV file downloaded successfully');
      } else {
        const error = await response.json();
        toast.error(`❌ Download failed: ${error.detail}`);
      }
    } catch (error) {
      toast.error(`❌ Download error: ${error.message}`);
    }
  };
  
  // Cancel upload
  const handleCancelUpload = async (taskId) => {
    try {
      const response = await fetch(`${BACKEND_URL}/api/upload/cancel/${taskId}`, {
        method: 'DELETE'
      });
      
      if (response.ok) {
        toast.success('✅ Upload cancelled');
        fetchQueueStatus();
      } else {
        toast.error('❌ Failed to cancel upload');
      }
    } catch (error) {
      toast.error(`❌ Cancel error: ${error.message}`);
    }
  };
  
  // Clear completed uploads
  const handleClearCompleted = async () => {
    try {
      const response = await fetch(`${BACKEND_URL}/api/uploads/clear-completed`, {
        method: 'DELETE'
      });
      
      if (response.ok) {
        toast.success('✅ Completed uploads cleared');
        fetchQueueStatus();
      } else {
        toast.error('❌ Failed to clear completed uploads');
      }
    } catch (error) {
      toast.error(`❌ Clear error: ${error.message}`);
    }
  };
  
  // Clear entire queue
  const handleClearQueue = async () => {
    try {
      const response = await fetch(`${BACKEND_URL}/api/queue/clear`, {
        method: 'DELETE'
      });
      
      if (response.ok) {
        toast.success('✅ Entire queue cleared');
        fetchQueueStatus();
      } else {
        toast.error('❌ Failed to clear queue');
      }
    } catch (error) {
      toast.error(`❌ Clear queue error: ${error.message}`);
    }
  };
  
  // End all tasks
  const handleEndAllTasks = async () => {
    try {
      const response = await fetch(`${BACKEND_URL}/api/uploads/end-all`, {
        method: 'POST'
      });
      
      if (response.ok) {
        const result = await response.json();
        toast.success(`✅ ${result.message}`);
        fetchQueueStatus();
      } else {
        toast.error('❌ Failed to end all tasks');
      }
    } catch (error) {
      toast.error(`❌ End tasks error: ${error.message}`);
    }
  };
  
  // Format time remaining
  const formatTimeRemaining = (seconds) => {
    if (!seconds || seconds <= 0) return '0s';
    
    const hours = Math.floor(seconds / 3600);
    const minutes = Math.floor((seconds % 3600) / 60);
    const secs = seconds % 60;
    
    if (hours > 0) return `${hours}h ${minutes}m`;
    if (minutes > 0) return `${minutes}m ${secs}s`;
    return `${secs}s`;
  };
  
  return (
    <div className="min-h-screen bg-black text-gray-100">
      <Toaster position="top-right" />
      
      {/* Header */}
      <header className="bg-gray-900 border-b border-gray-800 shadow-lg">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-6">
          <div className="flex items-center justify-between">
            <div className="flex items-center space-x-3">
              <div className="w-10 h-10 bg-gradient-to-br from-blue-500 to-purple-600 rounded-lg flex items-center justify-center">
                <Upload className="w-6 h-6 text-white" />
              </div>
              <div>
                <h1 className="text-2xl font-bold text-white">Video Upload Automation Hub</h1>
                <p className="text-gray-400 text-sm">Upload to 5 platforms simultaneously</p>
              </div>
            </div>
            
            {/* Status indicators */}
            <div className="flex items-center space-x-4">
              <div className="flex items-center space-x-2 text-sm">
                <div className="w-3 h-3 bg-green-500 rounded-full pulse-glow"></div>
                <span className="text-gray-300">Connected</span>
              </div>
            </div>
          </div>
        </div>
      </header>
      
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        {/* Platform Status Cards */}
        <div className="mb-8">
          <h2 className="text-lg font-semibold mb-4 flex items-center">
            <Monitor className="w-5 h-5 mr-2 text-blue-400" />
            Platform Status
          </h2>
          <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-5 gap-4">
            {PLATFORMS.map((platform) => (
              <div key={platform.name} className={`bg-gray-900 border-2 rounded-lg p-4 ${platform.name}`}>
                <div className="flex items-center justify-between mb-2">
                  <span className="font-medium">{platform.display}</span>
                  <span className="text-2xl">{platform.icon}</span>
                </div>
                <div className="text-sm">
                  {platformStatus[platform.name]?.configured ? (
                    <span className="text-green-400 flex items-center">
                      <CheckCircle className="w-4 h-4 mr-1" />
                      Ready
                    </span>
                  ) : (
                    <span className="text-red-400 flex items-center">
                      <XCircle className="w-4 h-4 mr-1" />
                      API Key Missing
                    </span>
                  )}
                </div>
              </div>
            ))}
          </div>
        </div>
        
        {/* URL Input Section */}
        <div className="mb-8">
          <div className="bg-gray-900 rounded-xl border border-gray-800 shadow-lg">
            <div className="p-6">
              <h2 className="text-xl font-semibold mb-4 flex items-center">
                <Plus className="w-5 h-5 mr-2 text-green-400" />
                Add Remote URLs for Upload
              </h2>
              
              <form onSubmit={handleSubmit} className="space-y-4">
                <div>
                  <label className="block text-sm font-medium text-gray-300 mb-2">
                    Remote URLs (one per line, max 50)
                  </label>
                  <textarea
                    value={urls}
                    onChange={(e) => setUrls(e.target.value)}
                    placeholder="https://example.com/video1.mp4&#10;https://example.com/video2.mp4&#10;..."
                    className="w-full h-32 px-4 py-3 bg-gray-800 border border-gray-700 rounded-lg text-gray-100 placeholder-gray-500 focus:ring-2 focus:ring-blue-500 focus:border-transparent resize-none"
                    disabled={isSubmitting}
                  />
                  <div className="flex justify-between text-sm text-gray-400 mt-2">
                    <span>
                      {urls.split('\n').filter(url => url.trim()).length} URLs entered
                    </span>
                    <span>
                      Queue: {queueStatus.total_queued} | Active: {queueStatus.total_in_progress}
                    </span>
                  </div>
                </div>
                
                <div className="flex items-center justify-between">
                  <div className="flex space-x-4">
                    <button
                      type="submit"
                      disabled={isSubmitting || !urls.trim()}
                      className="flex items-center px-6 py-3 bg-blue-600 hover:bg-blue-500 disabled:bg-gray-700 disabled:cursor-not-allowed text-white font-medium rounded-lg transition-colors duration-200"
                    >
                      {isSubmitting ? (
                        <>
                          <Loader2 className="w-5 h-5 mr-2 animate-spin" />
                          Queueing...
                        </>
                      ) : (
                        <>
                          <Upload className="w-5 h-5 mr-2" />
                          Queue for Upload
                        </>
                      )}
                    </button>
                    
                    <button
                      type="button"
                      onClick={handleDownloadCSV}
                      disabled={queueStatus.total_completed === 0}
                      className="flex items-center px-6 py-3 bg-green-600 hover:bg-green-500 disabled:bg-gray-700 disabled:cursor-not-allowed text-white font-medium rounded-lg transition-colors duration-200"
                    >
                      <Download className="w-5 h-5 mr-2" />
                      Download CSV ({queueStatus.total_completed})
                    </button>
                    
                    <button
                      type="button"
                      onClick={handleClearCompleted}
                      disabled={queueStatus.total_completed === 0}
                      className="flex items-center px-4 py-3 bg-yellow-600 hover:bg-yellow-500 disabled:bg-gray-700 disabled:cursor-not-allowed text-white font-medium rounded-lg transition-colors duration-200"
                    >
                      <Trash2 className="w-5 h-5 mr-2" />
                      Clear Completed
                    </button>
                    
                    <button
                      type="button"
                      onClick={handleClearQueue}
                      disabled={queueStatus.total_queued === 0 && queueStatus.total_in_progress === 0}
                      className="flex items-center px-4 py-3 bg-orange-600 hover:bg-orange-500 disabled:bg-gray-700 disabled:cursor-not-allowed text-white font-medium rounded-lg transition-colors duration-200"
                    >
                      <Trash2 className="w-5 h-5 mr-2" />
                      Clear Queue
                    </button>
                    
                    <button
                      type="button"
                      onClick={handleEndAllTasks}
                      disabled={queueStatus.total_in_progress === 0 && queueStatus.total_queued === 0}
                      className="flex items-center px-4 py-3 bg-red-600 hover:bg-red-500 disabled:bg-gray-700 disabled:cursor-not-allowed text-white font-medium rounded-lg transition-colors duration-200"
                    >
                      <XCircle className="w-5 h-5 mr-2" />
                      End All Tasks
                    </button>
                  </div>
                  
                  <div className="text-sm text-gray-400">
                    Total Completed: {queueStatus.total_completed} | Failed: {queueStatus.total_failed}
                  </div>
                </div>
              </form>
            </div>
          </div>
        </div>
        
        {/* Queue Status Overview */}
        <div className="mb-8">
          <h2 className="text-lg font-semibold mb-4 flex items-center">
            <BarChart3 className="w-5 h-5 mr-2 text-yellow-400" />
            Upload Queue Overview
          </h2>
          <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-4">
            <div className="bg-gray-900 border border-gray-800 rounded-lg p-4">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm text-gray-400">Queued</p>
                  <p className="text-2xl font-bold text-yellow-400">{queueStatus.total_queued}</p>
                </div>
                <Clock className="w-8 h-8 text-yellow-400" />
              </div>
            </div>
            
            <div className="bg-gray-900 border border-gray-800 rounded-lg p-4">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm text-gray-400">In Progress</p>
                  <p className="text-2xl font-bold text-blue-400">{queueStatus.total_in_progress}</p>
                </div>
                <Zap className="w-8 h-8 text-blue-400" />
              </div>
            </div>
            
            <div className="bg-gray-900 border border-gray-800 rounded-lg p-4">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm text-gray-400">Completed</p>
                  <p className="text-2xl font-bold text-green-400">{queueStatus.total_completed}</p>
                </div>
                <CheckCircle className="w-8 h-8 text-green-400" />
              </div>
            </div>
            
            <div className="bg-gray-900 border border-gray-800 rounded-lg p-4">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm text-gray-400">Failed</p>
                  <p className="text-2xl font-bold text-red-400">{queueStatus.total_failed}</p>
                </div>
                <XCircle className="w-8 h-8 text-red-400" />
              </div>
            </div>
          </div>
        </div>
        
        {/* Active Uploads Section */}
        {queueStatus.active_uploads.length > 0 && (
          <div className="mb-8">
            <h2 className="text-lg font-semibold mb-4 flex items-center">
              <Zap className="w-5 h-5 mr-2 text-blue-400" />
              Active Uploads ({queueStatus.active_uploads.length})
            </h2>
            <div className="space-y-4">
              {queueStatus.active_uploads.map((task) => (
                <div key={task.id} className="bg-gray-900 border border-gray-800 rounded-lg p-6 upload-card transition-all duration-200">
                  <div className="flex items-center justify-between mb-4">
                    <div className="flex-1">
                      <h3 className="font-medium text-white truncate">{task.filename}</h3>
                      <p className="text-sm text-gray-400 truncate">{task.url}</p>
                    </div>
                    <div className="flex items-center space-x-2">
                      <span className="text-sm text-blue-400 capitalize">{task.status.replace('_', ' ')}</span>
                      <button
                        onClick={() => handleCancelUpload(task.id)}
                        className="p-2 text-red-400 hover:text-red-300 hover:bg-red-900 rounded-lg transition-colors"
                        title="End This Task"
                      >
                        <XCircle className="w-4 h-4" />
                      </button>
                    </div>
                  </div>
                  
                  {/* Platform Progress */}
                  <div className="space-y-3">
                    {PLATFORMS.map((platform) => {
                      const progress = task.progress?.[platform.name] || 0;
                      const speed = task.speeds?.[platform.name] || 0;
                      const eta = task.eta_seconds?.[platform.name] || 0;
                      const status = task.platform_status?.[platform.name] || 'queued';
                      const hasEmbedCode = task.embed_codes?.[platform.name];
                      
                      return (
                        <div key={platform.name} className={`p-3 bg-gray-800 rounded-lg border-l-4 ${platform.name}`}>
                          <div className="flex items-center justify-between mb-2">
                            <div className="flex items-center space-x-2">
                              <span className="text-xl">{platform.icon}</span>
                              <span className="font-medium">{platform.display}</span>
                              {hasEmbedCode && (
                                <CheckCircle className="w-4 h-4 text-green-400" />
                              )}
                            </div>
                            <div className="flex items-center space-x-3 text-sm">
                              <span className={`status-${status.replace('_', '-')}`}>
                                {status.replace('_', ' ').toUpperCase()}
                              </span>
                              {speed > 0 && (
                                <span className="text-gray-400">
                                  {speed.toFixed(1)} MB/s
                                </span>
                              )}
                              {eta > 0 && (
                                <span className="text-gray-400">
                                  ETA: {formatTimeRemaining(eta)}
                                </span>
                              )}
                            </div>
                          </div>
                          
                          {/* Progress Bar */}
                          <div className="w-full bg-gray-700 rounded-full h-2 overflow-hidden">
                            <div 
                              className={`h-full transition-all duration-300 progress-${platform.name}`}
                              style={{ width: `${Math.max(progress, 0)}%` }}
                            />
                          </div>
                          
                          <div className="flex justify-between text-xs text-gray-400 mt-1">
                            <span>{progress.toFixed(1)}%</span>
                            {task.error_messages?.[platform.name] && (
                              <span className="text-red-400 flex items-center">
                                <AlertTriangle className="w-3 h-3 mr-1" />
                                Error
                              </span>
                            )}
                          </div>
                        </div>
                      );
                    })}
                  </div>
                </div>
              ))}
            </div>
          </div>
        )}
        
        {/* Queue Section */}
        {queueStatus.current_queue.length > 0 && (
          <div className="mb-8">
            <h2 className="text-lg font-semibold mb-4 flex items-center">
              <Clock className="w-5 h-5 mr-2 text-yellow-400" />
              Upload Queue ({queueStatus.current_queue.length})
            </h2>
            <div className="bg-gray-900 border border-gray-800 rounded-lg p-4">
              <div className="space-y-2">
                {queueStatus.current_queue.slice(0, 10).map((task, index) => (
                  <div key={task.id} className="flex items-center justify-between p-3 bg-gray-800 rounded-lg">
                    <div className="flex items-center space-x-3">
                      <span className="text-sm text-gray-400">#{index + 1}</span>
                      <div>
                        <p className="font-medium text-white truncate">{task.filename}</p>
                        <p className="text-xs text-gray-500 truncate">{task.url}</p>
                      </div>
                    </div>
                    <div className="flex items-center space-x-2">
                      <span className="text-sm text-yellow-400">Waiting</span>
                      <Clock className="w-4 h-4 text-yellow-400" />
                    </div>
                  </div>
                ))}
                {queueStatus.current_queue.length > 10 && (
                  <div className="text-center py-2 text-gray-400 text-sm">
                    ... and {queueStatus.current_queue.length - 10} more
                  </div>
                )}
              </div>
            </div>
          </div>
        )}
        
        {/* Completed Uploads Section */}
        {queueStatus.completed_uploads.length > 0 && (
          <div className="mb-8">
            <h2 className="text-lg font-semibold mb-4 flex items-center">
              <CheckCircle className="w-5 h-5 mr-2 text-green-400" />
              Recent Completed Uploads ({queueStatus.completed_uploads.length})
            </h2>
            <div className="space-y-4">
              {queueStatus.completed_uploads.slice(-5).reverse().map((task) => (
                <div key={task.id} className="bg-gray-900 border border-green-800 rounded-lg p-4 fade-in">
                  <div className="flex items-center justify-between mb-3">
                    <div className="flex-1">
                      <h3 className="font-medium text-white flex items-center">
                        <CheckCircle className="w-4 h-4 text-green-400 mr-2" />
                        {task.filename}
                      </h3>
                      <p className="text-sm text-gray-400 truncate">{task.url}</p>
                    </div>
                    <div className="text-sm text-green-400">
                      Completed {task.completed_at && new Date(task.completed_at).toLocaleTimeString()}
                    </div>
                  </div>
                  
                  {/* Platform Status Grid */}
                  <div className="grid grid-cols-5 gap-2">
                    {PLATFORMS.map((platform) => {
                      const hasEmbedCode = task.embed_codes?.[platform.name];
                      const status = task.platform_status?.[platform.name] || 'unknown';
                      
                      return (
                        <div key={platform.name} className={`p-2 rounded text-center text-xs ${platform.name}`}>
                          <div className="flex items-center justify-center space-x-1">
                            <span>{platform.icon}</span>
                            {hasEmbedCode ? (
                              <CheckCircle className="w-3 h-3 text-green-400" />
                            ) : (
                              <XCircle className="w-3 h-3 text-red-400" />
                            )}
                          </div>
                          <div className="mt-1 font-medium">{platform.display}</div>
                        </div>
                      );
                    })}
                  </div>
                </div>
              ))}
            </div>
          </div>
        )}
        
        {/* Footer */}
        <footer className="text-center py-8 text-gray-500 text-sm border-t border-gray-800">
          <p>Video Upload Automation Hub - Upload to multiple platforms simultaneously</p>
          <p className="mt-1">Supports Lulustream, StreamP2P, RPMShare, FileMoon, and UpnShare</p>
        </footer>
      </div>
    </div>
  );
}

export default App;