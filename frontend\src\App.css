/* Additional custom styles for the App component */

.App {
  text-align: center;
}

/* Progress bar styles */
.progress-bar {
  position: relative;
  overflow: hidden;
  border-radius: 0.25rem;
}

.progress-fill {
  height: 100%;
  transition: width 0.3s ease;
  border-radius: 0.25rem;
}

/* Platform-specific gradient progress bars */
.progress-lulustream {
  background: linear-gradient(90deg, #8b5cf6, #a855f7);
}

.progress-streamp2p {
  background: linear-gradient(90deg, #3b82f6, #2563eb);
}

.progress-rpmshare {
  background: linear-gradient(90deg, #10b981, #059669);
}

.progress-filemoon {
  background: linear-gradient(90deg, #f59e0b, #d97706);
}

.progress-upnshare {
  background: linear-gradient(90deg, #ec4899, #db2777);
}

/* Card hover effects */
.upload-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 10px 25px rgba(0, 0, 0, 0.4);
}

.platform-card:hover {
  box-shadow: 0 0 20px rgba(59, 130, 246, 0.3);
}

/* Button focus states */
.btn-primary:focus {
  outline: none;
  box-shadow: 0 0 0 2px #3b82f6, 0 0 0 4px rgba(59, 130, 246, 0.2);
}

/* Loading spinner */
.spinner {
  animation: spin 1s linear infinite;
}

@keyframes spin {
  from {
    transform: rotate(0deg);
  }
  to {
    transform: rotate(360deg);
  }
}

/* Fade-in animation for new items */
@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.fade-in {
  animation: fadeIn 0.3s ease-out;
}

/* Status badge animations */
.status-badge {
  transition: all 0.2s ease;
}

.status-completed {
  animation: pulse 2s ease-in-out;
}

@keyframes pulse {
  0%, 100% {
    opacity: 1;
  }
  50% {
    opacity: 0.7;
  }
}

/* Custom tooltip styles */
.tooltip {
  position: relative;
}

.tooltip:hover::after {
  content: attr(data-tooltip);
  position: absolute;
  bottom: 100%;
  left: 50%;
  transform: translateX(-50%);
  background: #1f2937;
  color: white;
  padding: 0.5rem;
  border-radius: 0.25rem;
  font-size: 0.875rem;
  white-space: nowrap;
  z-index: 1000;
  border: 1px solid #374151;
}

.tooltip:hover::before {
  content: '';
  position: absolute;
  bottom: 100%;
  left: 50%;
  transform: translateX(-50%) translateY(1px);
  border: 5px solid transparent;
  border-top-color: #1f2937;
  z-index: 1000;
}