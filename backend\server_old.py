import os
import asyncio
import uuid
import time
import json
import csv
import aiohttp
import aiofiles
from datetime import datetime, timedelta
from typing import Dict, List, Optional, Any, Union
from enum import Enum
from pathlib import Path

from fastapi import FastAPI, WebSocket, WebSocketDisconnect, HTTPException, UploadFile, File
from fastapi.middleware.cors import CORSMiddleware
from fastapi.responses import JSONResponse, FileResponse
from fastapi.staticfiles import StaticFiles
from pydantic import BaseModel, Field
from motor.motor_asyncio import AsyncIOMotorClient
from dotenv import load_dotenv

# Load environment variables
load_dotenv()

# Configuration
MONGO_URL = os.environ.get('MONGO_URL', 'mongodb://localhost:27017/video_upload_automation')
MAX_CONCURRENT_UPLOADS = int(os.environ.get('MAX_CONCURRENT_UPLOADS', '5'))
MAX_QUEUE_SIZE = int(os.environ.get('MAX_QUEUE_SIZE', '50'))
UPLOAD_TIMEOUT_MINUTES = int(os.environ.get('UPLOAD_TIMEOUT_MINUTES', '30'))

# Platform API Keys
PLATFORM_API_KEYS = {
    'lulustream': os.environ.get('LULUSTREAM_API_KEY', ''),
    'streamp2p': os.environ.get('STREAMP2P_API_KEY', ''),
    'rpmshare': os.environ.get('RPMSHARE_API_KEY', ''),
    'filemoon': os.environ.get('FILEMOON_API_KEY', ''),
    'upnshare': os.environ.get('UPNSHARE_API_KEY', '')
}

# Enums
class Platform(str, Enum):
    LULUSTREAM = "lulustream"
    STREAMP2P = "streamp2p" 
    RPMSHARE = "rpmshare"
    FILEMOON = "filemoon"
    UPNSHARE = "upnshare"

class UploadStatus(str, Enum):
    QUEUED = "queued"
    IN_PROGRESS = "in_progress"
    COMPLETED = "completed"
    FAILED = "failed"
    CANCELLED = "cancelled"

class UploadType(str, Enum):
    REMOTE_URL = "remote_url"

# Platform Configurations based on API documentation
PLATFORM_CONFIGS = {
    Platform.LULUSTREAM: {
        "api_key": PLATFORM_API_KEYS['lulustream'],
        "base_url": "https://lulustream.com/api",
        "upload_endpoint": "/upload/url",
        "status_endpoint": "/file/url_uploads",
        "info_endpoint": "/file/info",
        "embed_template": '<iframe src="https://luluvid.com/e/{file_code}" scrolling="no" frameborder="0" width="640" height="360" allowfullscreen="true" webkitallowfullscreen="true" mozallowfullscreen="true"></iframe>',
        "method": "GET"
    },
    Platform.STREAMP2P: {
        "api_key": PLATFORM_API_KEYS['streamp2p'],
        "base_url": "https://streamp2p.com/api/v1",
        "upload_endpoint": "/video/advance-upload",
        "status_endpoint": "/video/advance-upload",
        "info_endpoint": "/video/manage",
        "embed_template": '<iframe src="https://streamdb.p2pstream.online/#{file_code}" width="100%" height="100%" frameborder="0" allowfullscreen></iframe>',
        "method": "POST",
        "headers": {"api-token": PLATFORM_API_KEYS['streamp2p'], "Content-Type": "application/json"}
    },
    Platform.RPMSHARE: {
        "api_key": PLATFORM_API_KEYS['rpmshare'],
        "base_url": "https://rpmshare.com/api/v1",
        "upload_endpoint": "/video/advance-upload",
        "status_endpoint": "/video/advance-upload",
        "info_endpoint": "/video/manage",
        "embed_template": '<iframe src="https://streamdb.rpmstream.online/#{file_code}" width="100%" height="100%" frameborder="0" allowfullscreen></iframe>',
        "method": "POST",
        "headers": {"api-token": PLATFORM_API_KEYS['rpmshare'], "Content-Type": "application/json"}
    },
    Platform.FILEMOON: {
        "api_key": PLATFORM_API_KEYS['filemoon'],
        "base_url": "https://filemoonapi.com/api",
        "upload_endpoint": "/remote/add",
        "status_endpoint": "/remote/status",
        "info_endpoint": "/file/info",
        "embed_template": '<iframe src="https://filemoon.to/e/{file_code}" frameborder="0" marginwidth="0" marginheight="0" scrolling="no" width="640" height="360" allowfullscreen></iframe>',
        "method": "GET"
    },
    Platform.UPNSHARE: {
        "api_key": PLATFORM_API_KEYS['upnshare'],
        "base_url": "https://upnshare.com/api/v1",
        "upload_endpoint": "/video/advance-upload",
        "status_endpoint": "/video/advance-upload", 
        "info_endpoint": "/video/manage",
        "embed_template": '<iframe src="https://streamdb.upns.online/#{file_code}" width="100%" height="100%" frameborder="0" allowfullscreen></iframe>',
        "method": "POST",
        "headers": {"api-token": PLATFORM_API_KEYS['upnshare'], "Content-Type": "application/json"}
    }
}

# Pydantic Models
class UploadTask(BaseModel):
    id: str = Field(default_factory=lambda: str(uuid.uuid4()))
    url: str
    filename: str
    upload_type: UploadType = UploadType.REMOTE_URL
    status: UploadStatus = UploadStatus.QUEUED
    session_id: str
    
    # Progress tracking
    progress: Dict[str, float] = Field(default_factory=dict)  # Platform -> percentage
    speeds: Dict[str, float] = Field(default_factory=dict)    # Platform -> MB/s
    eta_seconds: Dict[str, int] = Field(default_factory=dict) # Platform -> seconds
    embed_codes: Dict[str, str] = Field(default_factory=dict) # Platform -> embed code
    file_codes: Dict[str, str] = Field(default_factory=dict)  # Platform -> file code
    
    # Timestamps
    created_at: datetime = Field(default_factory=datetime.utcnow)
    started_at: Optional[datetime] = None
    completed_at: Optional[datetime] = None
    
    # Status tracking
    platform_status: Dict[str, str] = Field(default_factory=dict)
    error_messages: Dict[str, str] = Field(default_factory=dict)
    
    class Config:
        json_encoders = {
            datetime: lambda v: v.isoformat()
        }

class UrlSubmissionRequest(BaseModel):
    urls: List[str]
    session_id: Optional[str] = None

class QueueStatusResponse(BaseModel):
    total_queued: int
    total_in_progress: int
    total_completed: int
    total_failed: int
    current_queue: List[UploadTask]
    active_uploads: List[UploadTask]
    completed_uploads: List[UploadTask]

# Global state
upload_queue: List[UploadTask] = []
active_uploads: Dict[str, UploadTask] = {}
completed_uploads: Dict[str, UploadTask] = {}
failed_uploads: Dict[str, UploadTask] = {}
cancel_flags: Dict[str, asyncio.Event] = {}
running_platform_tasks: Dict[str, List[asyncio.Task]] = {}

# WebSocket connections
active_connections: List[WebSocket] = []

# FastAPI app
app = FastAPI(title="Video Upload Automation Hub", version="1.0.0")

# CORS middleware
app.add_middleware(
    CORSMiddleware,
    allow_origins=["http://localhost:3000", "http://127.0.0.1:3000"],
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# MongoDB client
mongo_client: Optional[AsyncIOMotorClient] = None
db = None

# WebSocket broadcast function
async def broadcast_task_update(task: UploadTask):
    """Broadcast task updates to all connected WebSocket clients"""
    if active_connections:
        message = {
            "type": "task_update",
            "data": task.dict()
        }
        disconnected = []
        for connection in active_connections:
            try:
                await connection.send_text(json.dumps(message))
            except:
                disconnected.append(connection)
        
        # Remove disconnected clients
        for conn in disconnected:
            if conn in active_connections:
                active_connections.remove(conn)

# Platform upload functions
async def upload_to_lulustream(session: aiohttp.ClientSession, task: UploadTask, config: dict, cancel_event: Optional[asyncio.Event]) -> Dict[str, Any]:
    """Upload to Lulustream using their API"""
    try:
        # Add timestamp to avoid duplicates
        timestamp = str(int(time.time()))
        unique_url = task.url
        if "?" in unique_url:
            unique_url += f"&_t={timestamp}&_id={task.id[:8]}"
        else:
            unique_url += f"?_t={timestamp}&_id={task.id[:8]}"
        
        # Step 1: Submit upload request
        upload_url = f"{config['base_url']}{config['upload_endpoint']}"
        params = {
            "key": config["api_key"],
            "url": unique_url,
            "file_public": 1,
            "fld_id": 0
        }
        
        await _update_progress(task, Platform.LULUSTREAM, 1, 10, 5, "uploading")
        
        async with session.get(upload_url, params=params, timeout=aiohttp.ClientTimeout(total=120)) as response:
            if response.status == 200:
                result = await response.json()
                
                if result.get("status") == 200 and result.get("result"):
                    file_code = None
                    if isinstance(result["result"], dict):
                        file_code = result["result"].get("filecode") or result["result"].get("file_code")
                    elif isinstance(result["result"], str):
                        file_code = result["result"]
                    
                    if file_code:
                        await _update_progress(task, Platform.LULUSTREAM, 3, 10, 5, "processing")
                        
                        # Step 2: Poll for completion
                        max_attempts = 60
                        for attempt in range(max_attempts):
                            if cancel_event and cancel_event.is_set():
                                return {"error": "Upload cancelled"}
                            
                            # Check file info
                            info_url = f"{config['base_url']}{config['info_endpoint']}"
                            info_params = {"key": config["api_key"], "file_code": file_code}
                            
                            async with session.get(info_url, params=info_params) as info_response:
                                if info_response.status == 200:
                                    info_result = await info_response.json()
                                    
                                    if info_result.get("status") == 200 and info_result.get("result"):
                                        file_info = info_result["result"][0] if isinstance(info_result["result"], list) else info_result["result"]
                                        
                                        can_play = file_info.get("canplay")
                                        status = file_info.get("status")
                                        
                                        if can_play in [1, "1", True] or status in ["ready", "completed", "active"]:
                                            # Generate embed code
                                            embed_code = config["embed_template"].format(file_code=file_code)
                                            await _update_progress(task, Platform.LULUSTREAM, 10, 10, 5, "completed")
                                            await _save_embed_code(task, Platform.LULUSTREAM, embed_code, file_code, task.filename)
                                            return {"file_code": file_code, "embed_code": embed_code, "status": "completed"}
                            
                            # Update progress during polling
                            current_step = min(3 + (attempt // 3), 9)
                            await _update_progress(task, Platform.LULUSTREAM, current_step, 10, 5, "processing")
                            await asyncio.sleep(5)
                        
                        # Timeout fallback
                        embed_code = config["embed_template"].format(file_code=file_code)
                        await _update_progress(task, Platform.LULUSTREAM, 10, 10, 5, "completed")
                        await _save_embed_code(task, Platform.LULUSTREAM, embed_code, file_code, task.filename)
                        return {"file_code": file_code, "embed_code": embed_code, "status": "completed"}
                
                return {"error": f"Upload failed: {result.get('msg', 'Unknown error')}"}
            else:
                return {"error": f"HTTP {response.status}: Upload request failed"}
                
    except Exception as e:
        return {"error": f"Exception during upload: {str(e)}"}

async def upload_to_streamp2p(session: aiohttp.ClientSession, task: UploadTask, config: dict, cancel_event: Optional[asyncio.Event]) -> Dict[str, Any]:
    """Upload to StreamP2P using their API"""
    try:
        timestamp = str(int(time.time()))
        unique_url = task.url
        if "?" in unique_url:
            unique_url += f"&_t={timestamp}&_id={task.id[:8]}"
        else:
            unique_url += f"?_t={timestamp}&_id={task.id[:8]}"
        
        # Step 1: Submit upload request
        upload_url = f"{config['base_url']}{config['upload_endpoint']}"
        headers = {
            "api-token": config['api_key'],
            "Content-Type": "application/json",
            "Accept": "application/json"
        }
        data = {
            "url": unique_url,
            "title": task.filename,
            "description": f"Uploaded via automation - {task.filename}"
        }
        
        await _update_progress(task, Platform.STREAMP2P, 1, 12, 5, "uploading")
        
        async with session.post(upload_url, headers=headers, json=data, timeout=aiohttp.ClientTimeout(total=300)) as response:
            if response.status in (200, 201):
                result = await response.json()
                upload_id = result.get("id") or result.get("data", {}).get("id")
                
                if upload_id:
                    await _update_progress(task, Platform.STREAMP2P, 3, 12, 5, "processing")
                    
                    # Step 2: Poll for completion
                    max_attempts = 72
                    consecutive_failures = 0
                    
                    for attempt in range(max_attempts):
                        if cancel_event and cancel_event.is_set():
                            return {"error": "Upload cancelled"}
                        
                        try:
                            status_url = f"{config['base_url']}{config['status_endpoint']}/{upload_id}"
                            async with session.get(status_url, headers=headers) as status_response:
                                if status_response.status == 200:
                                    status_result = await status_response.json()
                                    upload_status = status_result.get("status") or status_result.get("data", {}).get("status")
                                    
                                    if upload_status in ("completed", "ready", "success"):
                                        file_code = status_result.get("file_code") or upload_id
                                        embed_code = config["embed_template"].format(file_code=file_code)
                                        await _update_progress(task, Platform.STREAMP2P, 12, 12, 5, "completed")
                                        await _save_embed_code(task, Platform.STREAMP2P, embed_code, file_code, task.filename)
                                        return {"file_code": file_code, "embed_code": embed_code, "status": "completed"}
                                    
                                    consecutive_failures = 0
                                else:
                                    consecutive_failures += 1
                        except:
                            consecutive_failures += 1
                        
                        # Timeout fallback strategy
                        if consecutive_failures >= 10 and attempt > 20:
                            file_code = upload_id
                            embed_code = config["embed_template"].format(file_code=file_code)
                            await _update_progress(task, Platform.STREAMP2P, 12, 12, 5, "completed")
                            await _save_embed_code(task, Platform.STREAMP2P, embed_code, file_code, task.filename)
                            return {"file_code": file_code, "embed_code": embed_code, "status": "completed"}
                        
                        # Update progress
                        current_step = min(3 + (attempt // 6), 11)
                        await _update_progress(task, Platform.STREAMP2P, current_step, 12, 5, "processing")
                        await asyncio.sleep(5)
                
                return {"error": f"No upload ID returned"}
            else:
                return {"error": f"HTTP {response.status}: Upload request failed"}
                
    except Exception as e:
        return {"error": f"Exception during upload: {str(e)}"}

async def upload_to_rpmshare(session: aiohttp.ClientSession, task: UploadTask, config: dict, cancel_event: Optional[asyncio.Event]) -> Dict[str, Any]:
    """Upload to RPMShare using their API (same pattern as StreamP2P)"""
    try:
        timestamp = str(int(time.time()))
        unique_url = task.url
        if "?" in unique_url:
            unique_url += f"&_t={timestamp}&_id={task.id[:8]}"
        else:
            unique_url += f"?_t={timestamp}&_id={task.id[:8]}"
        
        # Step 1: Submit upload request
        upload_url = f"{config['base_url']}{config['upload_endpoint']}"
        headers = {
            "api-token": config['api_key'],
            "Content-Type": "application/json",
            "Accept": "application/json"
        }
        data = {
            "url": unique_url,
            "title": task.filename,
            "description": f"Uploaded via automation - {task.filename}"
        }
        
        await _update_progress(task, Platform.RPMSHARE, 1, 12, 5, "uploading")
        
        async with session.post(upload_url, headers=headers, json=data, timeout=aiohttp.ClientTimeout(total=300)) as response:
            if response.status in (200, 201):
                result = await response.json()
                upload_id = result.get("id") or result.get("data", {}).get("id")
                
                if upload_id:
                    await _update_progress(task, Platform.RPMSHARE, 3, 12, 5, "processing")
                    
                    # Step 2: Poll for completion
                    max_attempts = 72
                    consecutive_failures = 0
                    
                    for attempt in range(max_attempts):
                        if cancel_event and cancel_event.is_set():
                            return {"error": "Upload cancelled"}
                        
                        try:
                            status_url = f"{config['base_url']}{config['status_endpoint']}/{upload_id}"
                            async with session.get(status_url, headers=headers) as status_response:
                                if status_response.status == 200:
                                    status_result = await status_response.json()
                                    upload_status = status_result.get("status") or status_result.get("data", {}).get("status")
                                    
                                    if upload_status in ("completed", "ready", "success"):
                                        file_code = status_result.get("file_code") or upload_id
                                        embed_code = config["embed_template"].format(file_code=file_code)
                                        await _update_progress(task, Platform.RPMSHARE, 12, 12, 5, "completed")
                                        await _save_embed_code(task, Platform.RPMSHARE, embed_code, file_code, task.filename)
                                        return {"file_code": file_code, "embed_code": embed_code, "status": "completed"}
                                    
                                    consecutive_failures = 0
                                else:
                                    consecutive_failures += 1
                        except:
                            consecutive_failures += 1
                        
                        # Timeout fallback strategy
                        if consecutive_failures >= 10 and attempt > 20:
                            file_code = upload_id
                            embed_code = config["embed_template"].format(file_code=file_code)
                            await _update_progress(task, Platform.RPMSHARE, 12, 12, 5, "completed")
                            await _save_embed_code(task, Platform.RPMSHARE, embed_code, file_code, task.filename)
                            return {"file_code": file_code, "embed_code": embed_code, "status": "completed"}
                        
                        # Update progress
                        current_step = min(3 + (attempt // 6), 11)
                        await _update_progress(task, Platform.RPMSHARE, current_step, 12, 5, "processing")
                        await asyncio.sleep(5)
                
                return {"error": f"No upload ID returned"}
            else:
                return {"error": f"HTTP {response.status}: Upload request failed"}
                
    except Exception as e:
        return {"error": f"Exception during upload: {str(e)}"}

async def upload_to_filemoon(session: aiohttp.ClientSession, task: UploadTask, config: dict, cancel_event: Optional[asyncio.Event]) -> Dict[str, Any]:
    """Upload to FileMoon using their API"""
    try:
        timestamp = str(int(time.time()))
        unique_url = task.url
        if "?" in unique_url:
            unique_url += f"&_t={timestamp}&_id={task.id[:8]}"
        else:
            unique_url += f"?_t={timestamp}&_id={task.id[:8]}"
        
        # Step 1: Submit remote upload
        upload_url = f"{config['base_url']}{config['upload_endpoint']}"
        params = {
            "key": config["api_key"],
            "url": unique_url
        }
        
        await _update_progress(task, Platform.FILEMOON, 1, 20, 5, "uploading")
        
        async with session.get(upload_url, params=params, timeout=aiohttp.ClientTimeout(total=120)) as response:
            if response.status == 200:
                result = await response.json()
                
                if result.get("status") == 200 and result.get("result", {}).get("filecode"):
                    file_code = result["result"]["filecode"]
                    await _update_progress(task, Platform.FILEMOON, 3, 20, 5, "processing")
                    
                    # Step 2: Monitor remote upload status
                    max_attempts = 120
                    for attempt in range(max_attempts):
                        if cancel_event and cancel_event.is_set():
                            return {"error": "Upload cancelled"}
                        
                        status_url = f"{config['base_url']}{config['status_endpoint']}"
                        status_params = {"key": config["api_key"], "file_code": file_code}
                        
                        async with session.get(status_url, params=status_params) as status_response:
                            if status_response.status == 200:
                                status_result = await status_response.json()
                                
                                if status_result.get("status") == 200:
                                    upload_status = status_result["result"].get("status")
                                    
                                    if upload_status == "COMPLETED":
                                        # Get file info for actual filename
                                        info_url = f"{config['base_url']}{config['info_endpoint']}"
                                        info_params = {"key": config["api_key"], "file_code": file_code}
                                        
                                        async with session.get(info_url, params=info_params) as info_response:
                                            if info_response.status == 200:
                                                info_result = await info_response.json()
                                                if info_result.get("status") == 200 and info_result.get("result"):
                                                    file_info = info_result["result"][0] if isinstance(info_result["result"], list) else info_result["result"]
                                                    real_filename = file_info.get("name", task.filename)
                                                    
                                                    embed_code = config["embed_template"].format(file_code=file_code)
                                                    await _update_progress(task, Platform.FILEMOON, 20, 20, 5, "completed")
                                                    await _save_embed_code(task, Platform.FILEMOON, embed_code, file_code, real_filename)
                                                    return {"file_code": file_code, "embed_code": embed_code, "status": "completed"}
                                    
                                    elif upload_status in ["ERROR", "FAILED"]:
                                        return {"error": f"Upload failed with status: {upload_status}"}
                        
                        # Update progress
                        current_step = min(3 + (attempt // 6), 19)
                        await _update_progress(task, Platform.FILEMOON, current_step, 20, 5, "processing")
                        await asyncio.sleep(5)
                    
                    # Timeout fallback
                    embed_code = config["embed_template"].format(file_code=file_code)
                    await _update_progress(task, Platform.FILEMOON, 20, 20, 5, "completed")
                    await _save_embed_code(task, Platform.FILEMOON, embed_code, file_code, task.filename)
                    return {"file_code": file_code, "embed_code": embed_code, "status": "completed"}
                
                return {"error": f"Upload failed: {result.get('msg', 'Unknown error')}"}
            else:
                return {"error": f"HTTP {response.status}: Upload request failed"}
                
    except Exception as e:
        return {"error": f"Exception during upload: {str(e)}"}

async def upload_to_upnshare(session: aiohttp.ClientSession, task: UploadTask, config: dict, cancel_event: Optional[asyncio.Event]) -> Dict[str, Any]:
    """Upload to UpnShare using their API (same pattern as StreamP2P/RPMShare)"""
    try:
        timestamp = str(int(time.time()))
        unique_url = task.url
        if "?" in unique_url:
            unique_url += f"&_t={timestamp}&_id={task.id[:8]}"
        else:
            unique_url += f"?_t={timestamp}&_id={task.id[:8]}"
        
        # Step 1: Submit upload request
        upload_url = f"{config['base_url']}{config['upload_endpoint']}"
        headers = {
            "api-token": config['api_key'],
            "Content-Type": "application/json",
            "Accept": "application/json"
        }
        data = {
            "url": unique_url,
            "title": task.filename,
            "description": f"Uploaded via automation - {task.filename}"
        }
        
        await _update_progress(task, Platform.UPNSHARE, 1, 12, 5, "uploading")
        
        async with session.post(upload_url, headers=headers, json=data, timeout=aiohttp.ClientTimeout(total=300)) as response:
            if response.status in (200, 201):
                result = await response.json()
                upload_id = result.get("id") or result.get("data", {}).get("id")
                
                if upload_id:
                    await _update_progress(task, Platform.UPNSHARE, 3, 12, 5, "processing")
                    
                    # Step 2: Poll for completion
                    max_attempts = 72
                    consecutive_failures = 0
                    
                    for attempt in range(max_attempts):
                        if cancel_event and cancel_event.is_set():
                            return {"error": "Upload cancelled"}
                        
                        try:
                            status_url = f"{config['base_url']}{config['status_endpoint']}/{upload_id}"
                            async with session.get(status_url, headers=headers) as status_response:
                                if status_response.status == 200:
                                    status_result = await status_response.json()
                                    upload_status = status_result.get("status") or status_result.get("data", {}).get("status")
                                    
                                    if upload_status in ("completed", "ready", "success"):
                                        file_code = status_result.get("file_code") or upload_id
                                        embed_code = config["embed_template"].format(file_code=file_code)
                                        await _update_progress(task, Platform.UPNSHARE, 12, 12, 5, "completed")
                                        await _save_embed_code(task, Platform.UPNSHARE, embed_code, file_code, task.filename)
                                        return {"file_code": file_code, "embed_code": embed_code, "status": "completed"}
                                    
                                    consecutive_failures = 0
                                else:
                                    consecutive_failures += 1
                        except:
                            consecutive_failures += 1
                        
                        # Timeout fallback strategy
                        if consecutive_failures >= 10 and attempt > 20:
                            file_code = upload_id
                            embed_code = config["embed_template"].format(file_code=file_code)
                            await _update_progress(task, Platform.UPNSHARE, 12, 12, 5, "completed")
                            await _save_embed_code(task, Platform.UPNSHARE, embed_code, file_code, task.filename)
                            return {"file_code": file_code, "embed_code": embed_code, "status": "completed"}
                        
                        # Update progress
                        current_step = min(3 + (attempt // 6), 11)
                        await _update_progress(task, Platform.UPNSHARE, current_step, 12, 5, "processing")
                        await asyncio.sleep(5)
                
                return {"error": f"No upload ID returned"}
            else:
                return {"error": f"HTTP {response.status}: Upload request failed"}
                
    except Exception as e:
        return {"error": f"Exception during upload: {str(e)}"}

# Helper functions
async def _update_progress(task: UploadTask, platform: Platform, current_step: int, total_steps: int, interval_s: int = 5, stage: str = "processing"):
    """Update progress for a specific platform"""
    stage_ranges = {
        "uploading": (0, 25),
        "processing": (25, 95),
        "completed": (100, 100)
    }
    
    base_min, base_max = stage_ranges.get(stage, (0, 95))
    
    if stage == "completed":
        percent = 100.0
        eta_seconds = 0
        speed_mbps = 0.0
    else:
        stage_progress = min(current_step / max(total_steps, 1), 1.0)
        percent = base_min + (stage_progress * (base_max - base_min))
        percent = max(0.0, min(percent, 95.0))
        
        remaining_steps = max(0, total_steps - current_step)
        eta_seconds = remaining_steps * interval_s
        
        if stage == "uploading":
            speed_mbps = 2.5
        elif stage == "processing":
            speed_mbps = 0.5
        else:
            speed_mbps = 1.0
    
    # Update task
    task.progress[platform.value] = round(percent, 1)
    task.eta_seconds[platform.value] = eta_seconds
    task.speeds[platform.value] = round(speed_mbps, 2)
    task.platform_status[platform.value] = stage
    
    # Broadcast update via WebSocket
    await broadcast_task_update(task)

async def _save_embed_code(task: UploadTask, platform: Platform, embed_code: str, file_code: str, filename: str):
    """Save embed code to task"""
    task.embed_codes[platform.value] = embed_code
    task.file_codes[platform.value] = file_code
    
    # Update filename if we got a better one
    if filename and filename != task.filename:
        task.filename = filename
    
    # Save to database
    if db:
        await db.tasks.update_one(
            {"id": task.id},
            {"$set": {
                "embed_codes": task.embed_codes,
                "file_codes": task.file_codes,
                "filename": task.filename
            }},
            upsert=True
        )

# Queue management functions
async def start_next_uploads():
    """Start next uploads maintaining concurrency limits"""
    while len(active_uploads) < MAX_CONCURRENT_UPLOADS and upload_queue:
        task = upload_queue.pop(0)
        task.status = UploadStatus.IN_PROGRESS
        task.started_at = datetime.utcnow()
        
        # Add to active uploads
        active_uploads[task.id] = task
        cancel_flags[task.id] = asyncio.Event()
        
        # Create platform upload tasks
        platform_tasks = []
        for platform in Platform:
            platform_task = asyncio.create_task(
                upload_to_platform(platform, task, cancel_flags[task.id])
            )
            platform_tasks.append(platform_task)
        
        running_platform_tasks[task.id] = platform_tasks
        
        # Monitor completion
        asyncio.create_task(monitor_upload_completion(task.id, platform_tasks))

async def upload_to_platform(platform: Platform, task: UploadTask, cancel_event: asyncio.Event):
    """Route upload to appropriate platform function"""
    config = PLATFORM_CONFIGS[platform]
    
    if not config["api_key"]:
        return {"error": f"{platform.value} API key not configured"}
    
    try:
        async with aiohttp.ClientSession() as session:
            if platform == Platform.LULUSTREAM:
                result = await upload_to_lulustream(session, task, config, cancel_event)
            elif platform == Platform.STREAMP2P:
                result = await upload_to_streamp2p(session, task, config, cancel_event)
            elif platform == Platform.RPMSHARE:
                result = await upload_to_rpmshare(session, task, config, cancel_event)
            elif platform == Platform.FILEMOON:
                result = await upload_to_filemoon(session, task, config, cancel_event)
            elif platform == Platform.UPNSHARE:
                result = await upload_to_upnshare(session, task, config, cancel_event)
            else:
                result = {"error": f"Unknown platform: {platform}"}
            
            # Handle result
            if "error" in result:
                task.error_messages[platform.value] = result["error"]
                task.platform_status[platform.value] = "failed"
            else:
                task.platform_status[platform.value] = "completed"
            
            return result
            
    except Exception as e:
        error_msg = f"Exception in {platform.value}: {str(e)}"
        task.error_messages[platform.value] = error_msg
        task.platform_status[platform.value] = "failed"
        return {"error": error_msg}

async def monitor_upload_completion(task_id: str, platform_tasks: List[asyncio.Task]):
    """Monitor when all platform uploads are complete for a task"""
    try:
        # Wait for all platform tasks to complete
        await asyncio.gather(*platform_tasks, return_exceptions=True)
        
        # Move task from active to completed
        if task_id in active_uploads:
            task = active_uploads.pop(task_id)
            task.completed_at = datetime.utcnow()
            
            # Check if all platforms completed successfully
            completed_platforms = sum(1 for status in task.platform_status.values() if status == "completed")
            total_platforms = len(Platform)
            
            if completed_platforms == total_platforms:
                task.status = UploadStatus.COMPLETED
                completed_uploads[task_id] = task
            elif completed_platforms > 0:
                task.status = UploadStatus.COMPLETED  # Partial success still counts as completed
                completed_uploads[task_id] = task
            else:
                task.status = UploadStatus.FAILED
                failed_uploads[task_id] = task
            
            # Clean up
            if task_id in cancel_flags:
                del cancel_flags[task_id]
            if task_id in running_platform_tasks:
                del running_platform_tasks[task_id]
            
            # Save to database
            if db:
                await db.tasks.replace_one(
                    {"id": task.id},
                    task.dict(),
                    upsert=True
                )
            
            # Broadcast final update
            await broadcast_task_update(task)
            
            # Start next uploads
            await start_next_uploads()
            
    except Exception as e:
        print(f"Error monitoring upload completion for task {task_id}: {e}")

# Database functions
async def connect_to_mongo():
    """Connect to MongoDB"""
    global mongo_client, db
    try:
        mongo_client = AsyncIOMotorClient(MONGO_URL)
        db = mongo_client.get_default_database()
        
        # Test connection
        await db.command("ping")
        print("Connected to MongoDB successfully")
        
        # Create indexes
        await db.tasks.create_index("id", unique=True)
        await db.tasks.create_index("session_id")
        await db.tasks.create_index("status")
        await db.tasks.create_index("created_at")
        
    except Exception as e:
        print(f"Failed to connect to MongoDB: {e}")
        # Continue without database for now
        mongo_client = None
        db = None

async def disconnect_from_mongo():
    """Disconnect from MongoDB"""
    global mongo_client
    if mongo_client:
        mongo_client.close()

# CSV Generation
async def generate_csv_file(session_id: str = None) -> str:
    """Generate CSV file with embed codes"""
    # Get completed tasks
    if session_id:
        tasks_to_export = [task for task in completed_uploads.values() if task.session_id == session_id]
    else:
        tasks_to_export = list(completed_uploads.values())
    
    if not tasks_to_export:
        raise HTTPException(status_code=404, detail="No completed uploads found")
    
    # Create CSV content
    timestamp = datetime.utcnow().strftime("%Y%m%d_%H%M%S")
    filename = f"embed_codes_{timestamp}.csv"
    filepath = f"/tmp/{filename}"
    
    async with aiofiles.open(filepath, 'w', newline='', encoding='utf-8') as csvfile:
        # Create CSV writer manually since aiofiles doesn't support csv module directly
        header = "Filename,Embed Codes\n"
        await csvfile.write(header)
        
        for task in tasks_to_export:
            filename_clean = task.filename.replace('"', '""')  # Escape quotes
            
            # Create embed codes in specified order with line breaks
            embed_lines = []
            
            # 1st line - Lulustream embed codes
            embed_lines.append(task.embed_codes.get('lulustream', ''))
            # 2nd line - Streamp2p embed codes  
            embed_lines.append(task.embed_codes.get('streamp2p', ''))
            # 3rd line - Rpmshare embed codes
            embed_lines.append(task.embed_codes.get('rpmshare', ''))
            # 4th line - Filemoon embed codes
            embed_lines.append(task.embed_codes.get('filemoon', ''))
            # 5th line - Upnshare embed codes
            embed_lines.append(task.embed_codes.get('upnshare', ''))
            
            # Join with double line breaks as specified
            embed_string = '\n\n'.join(embed_lines) if any(embed_lines) else "No embed codes available"
            embed_string_escaped = embed_string.replace('"', '""')  # Escape quotes
            
            # Write row
            row = f'"{filename_clean}","{embed_string_escaped}"\n'
            await csvfile.write(row)
    
    return filepath

# FastAPI Events
@app.on_event("startup")
async def startup_event():
    """Initialize database connection on startup"""
    await connect_to_mongo()
    
    # Create audio directories
    os.makedirs("/app/backend/audio", exist_ok=True)
    os.makedirs("/app/frontend/public/audio", exist_ok=True)

@app.on_event("shutdown")
async def shutdown_event():
    """Clean up on shutdown"""
    await disconnect_from_mongo()

# WebSocket endpoint
@app.websocket("/ws")
async def websocket_endpoint(websocket: WebSocket):
    await websocket.accept()
    active_connections.append(websocket)
    
    try:
        # Send current state
        current_state = {
            "type": "initial_state",
            "data": {
                "queue": [task.dict() for task in upload_queue],
                "active": [task.dict() for task in active_uploads.values()],
                "completed": [task.dict() for task in completed_uploads.values()],
                "failed": [task.dict() for task in failed_uploads.values()]
            }
        }
        await websocket.send_text(json.dumps(current_state))
        
        # Keep connection alive
        while True:
            try:
                # Receive ping/pong messages
                data = await websocket.receive_text()
                message = json.loads(data)
                
                if message.get("type") == "ping":
                    await websocket.send_text(json.dumps({"type": "pong"}))
                    
            except WebSocketDisconnect:
                break
                
    except Exception as e:
        print(f"WebSocket error: {e}")
    finally:
        if websocket in active_connections:
            active_connections.remove(websocket)

# API Endpoints
@app.post("/api/upload/submit", response_model=Dict[str, Any])
async def submit_urls(request: UrlSubmissionRequest):
    """Submit URLs for upload to all platforms"""
    
    if len(request.urls) > MAX_QUEUE_SIZE:
        raise HTTPException(status_code=400, detail=f"Maximum {MAX_QUEUE_SIZE} URLs allowed")
    
    if len(upload_queue) + len(request.urls) > MAX_QUEUE_SIZE:
        raise HTTPException(status_code=400, detail=f"Queue would exceed maximum size of {MAX_QUEUE_SIZE}")
    
    session_id = request.session_id or str(uuid.uuid4())
    created_tasks = []
    
    for url in request.urls:
        # Extract filename from URL
        filename = url.split('/')[-1].split('?')[0] or f"video_{len(created_tasks)+1}"
        if not filename.endswith(('.mp4', '.avi', '.mkv', '.mov', '.wmv', '.flv', '.webm')):
            filename += ".mp4"  # Default extension
        
        task = UploadTask(
            url=url,
            filename=filename,
            session_id=session_id
        )
        
        upload_queue.append(task)
        created_tasks.append(task)
        
        # Save to database
        if db:
            await db.tasks.insert_one(task.dict())
    
    # Start uploads if there's capacity
    await start_next_uploads()
    
    return {
        "message": f"Successfully queued {len(created_tasks)} URLs",
        "session_id": session_id,
        "tasks": [task.dict() for task in created_tasks]
    }

@app.get("/api/queue/status", response_model=QueueStatusResponse)
async def get_queue_status():
    """Get current queue status"""
    return QueueStatusResponse(
        total_queued=len(upload_queue),
        total_in_progress=len(active_uploads),
        total_completed=len(completed_uploads),
        total_failed=len(failed_uploads),
        current_queue=upload_queue[:10],  # Show first 10 in queue
        active_uploads=list(active_uploads.values()),
        completed_uploads=list(completed_uploads.values())[-20:]  # Show last 20 completed
    )

@app.delete("/api/upload/cancel/{task_id}")
async def cancel_upload(task_id: str):
    """Cancel a specific upload"""
    if task_id in cancel_flags:
        cancel_flags[task_id].set()
        
        # Remove from active uploads
        if task_id in active_uploads:
            task = active_uploads.pop(task_id)
            task.status = UploadStatus.CANCELLED
            failed_uploads[task_id] = task
            
            # Clean up
            if task_id in running_platform_tasks:
                tasks = running_platform_tasks.pop(task_id)
                for t in tasks:
                    t.cancel()
        
        # Start next upload
        await start_next_uploads()
        
        return {"message": "Upload cancelled"}
    else:
        raise HTTPException(status_code=404, detail="Upload not found")

@app.get("/api/download/csv")
async def download_csv(session_id: str = None):
    """Download CSV file with embed codes"""
    try:
        filepath = await generate_csv_file(session_id)
        filename = os.path.basename(filepath)
        
        return FileResponse(
            path=filepath,
            filename=filename,
            media_type='text/csv',
            headers={"Content-Disposition": f"attachment; filename={filename}"}
        )
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Failed to generate CSV: {str(e)}")

@app.get("/api/health")
async def health_check():
    """Health check endpoint"""
    return {
        "status": "healthy",
        "timestamp": datetime.utcnow().isoformat(),
        "queue_size": len(upload_queue),
        "active_uploads": len(active_uploads),
        "completed_uploads": len(completed_uploads),
        "database_connected": db is not None
    }

@app.get("/api/platforms/status")
async def get_platform_status():
    """Get status of all platforms (API keys configured)"""
    status = {}
    for platform, config in PLATFORM_CONFIGS.items():
        status[platform.value] = {
            "configured": bool(config["api_key"]),
            "base_url": config["base_url"]
        }
    return status

# Clear completed uploads endpoint  
@app.delete("/api/uploads/clear-completed")
async def clear_completed_uploads():
    """Clear completed uploads from memory"""
    global completed_uploads
    completed_uploads.clear()
    return {"message": "Completed uploads cleared"}

# Static files for audio
app.mount("/audio", StaticFiles(directory="/app/backend/audio"), name="audio")

if __name__ == "__main__":
    import uvicorn
    uvicorn.run(
        "server:app",
        host=os.environ.get("BACKEND_HOST", "0.0.0.0"),
        port=int(os.environ.get("BACKEND_PORT", "8001")),
        reload=True
    )