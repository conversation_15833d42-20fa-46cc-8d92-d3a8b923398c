# Dependencies
node_modules/
frontend/node_modules/
backend/__pycache__/
backend/.venv/
backend/venv/
**/node_modules/
**/node_modules/**/*

# Build outputs
frontend/build/
frontend/dist/
backend/dist/

# Cache directories
.cache/
**/.cache/
**/.cache/**/*
frontend/node_modules/.cache/
backend/.pytest_cache/

# Logs
*.log
logs/
backend/logs/
npm-debug.log*
yarn-debug.log*
yarn-error.log*

# Database
*.db
*.sqlite
*.sqlite3

# IDE
.vscode/
.idea/
*.swp
*.swo
.DS_Store

# OS
.DS_Store
Thumbs.db

# Temporary files
tmp/
temp/
uploads/
downloads/

# Test files
test_results/
coverage/

# Runtime data
pids
*.pid
*.seed
*.pid.lock

# Directory for instrumented libs generated by jscoverage/JSCover
lib-cov

# Coverage directory used by tools like istanbul
coverage
*.lcov

# nyc test coverage
.nyc_output

# ESLint cache
.eslintcache

# Optional npm cache directory
.npm

# Optional eslint cache
.eslintcache

# Microbundle cache
.rpt2_cache/
.rts2_cache_cjs/
.rts2_cache_es/
.rts2_cache_umd/

# Optional REPL history
.node_repl_history

# Output of 'npm pack'
*.tgz

# Yarn Integrity file
.yarn-integrity

# parcel-bundler cache (https://parceljs.org/)
.parcel-cache

# Git
.git/
.emergent/