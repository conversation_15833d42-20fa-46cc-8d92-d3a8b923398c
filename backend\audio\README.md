# Custom Audio Files

Place your custom notification audio files here:

- `completion.mp3` - Plays when all platforms complete upload for a file
- `error.mp3` - Plays when uploads fail on any platform

## Audio Requirements:
- Format: MP3
- Duration: 1-3 seconds recommended
- Volume: Medium (app will set to 30% automatically)
- File size: < 1MB recommended

## Usage:
The app will automatically play these sounds for:
- ✅ **Completion**: When all 5 platforms finish uploading a video
- ❌ **Error**: When any upload fails or encounters errors

Make sure to copy the same files to both:
- `/backend/audio/`
- `/frontend/public/audio/`