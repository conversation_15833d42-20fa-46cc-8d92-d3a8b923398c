@tailwind base;
@tailwind components;
@tailwind utilities;

/* Deep Black Dark Mode Theme */
@layer base {
  * {
    @apply border-gray-800;
  }
  
  body {
    @apply bg-black text-gray-100 antialiased;
    font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', sans-serif;
  }
  
  html {
    @apply bg-black;
  }
}

/* Custom scrollbar */
::-webkit-scrollbar {
  width: 8px;
  height: 8px;
}

::-webkit-scrollbar-track {
  @apply bg-gray-900;
}

::-webkit-scrollbar-thumb {
  @apply bg-gray-700 rounded-md;
}

::-webkit-scrollbar-thumb:hover {
  @apply bg-gray-600;
}

/* Custom animations */
@keyframes pulse-glow {
  0%, 100% {
    box-shadow: 0 0 5px rgba(59, 130, 246, 0.5);
  }
  50% {
    box-shadow: 0 0 20px rgba(59, 130, 246, 0.8);
  }
}

.pulse-glow {
  animation: pulse-glow 2s ease-in-out infinite;
}

@keyframes progress-indeterminate {
  0% {
    transform: translateX(-100%);
  }
  100% {
    transform: translateX(100%);
  }
}

.progress-indeterminate::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(90deg, transparent, rgba(59, 130, 246, 0.4), transparent);
  animation: progress-indeterminate 1.5s ease-in-out infinite;
}

/* Platform specific colors */
.lulustream { @apply text-purple-400 border-purple-500; }
.streamp2p { @apply text-blue-400 border-blue-500; }
.rpmshare { @apply text-green-400 border-green-500; }
.filemoon { @apply text-yellow-400 border-yellow-500; }
.upnshare { @apply text-pink-400 border-pink-500; }

/* Status colors */
.status-queued { @apply text-gray-400; }
.status-uploading { @apply text-blue-400; }
.status-processing { @apply text-yellow-400; }
.status-completed { @apply text-green-400; }
.status-failed { @apply text-red-400; }
.status-cancelled { @apply text-gray-500; }